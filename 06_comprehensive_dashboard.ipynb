# Import libraries and load all processed data
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.figure_factory as ff
import warnings
import json
from datetime import datetime

warnings.filterwarnings('ignore')
plt.style.use('seaborn-v0_8')

# Load all processed data
df = pd.read_csv('processed_inventory_data.csv')
warehouse_stats = pd.read_csv('warehouse_performance_stats.csv')
category_stats = pd.read_csv('category_performance_analysis.csv')
optimization_results = pd.read_csv('inventory_optimization_results.csv')

# Load summary statistics
with open('inventory_summary.json', 'r') as f:
    inventory_summary = json.load(f)

with open('optimization_summary.json', 'r') as f:
    optimization_summary = json.load(f)

print("All data loaded successfully")
print(f"Analysis date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

# Executive KPI Dashboard
print("=== EXECUTIVE DASHBOARD - KEY PERFORMANCE INDICATORS ===")

# Create KPI summary
kpis = {
    'Total Inventory Value': f"${inventory_summary['total_inventory_value']:,.2f}",
    'Total Products': f"{inventory_summary['total_products']:,}",
    'Active Warehouses': f"{len(warehouse_stats)}",
    'Product Categories': f"{inventory_summary['categories_count']}",
    'Available Units': f"{inventory_summary['total_available_units']:,}",
    'Average Product Price': f"${inventory_summary['avg_product_price']:.2f}",
    'Optimization Potential': f"${optimization_summary['optimization_potential_value']:,.2f}",
    'Out of Stock Items': f"{optimization_summary['out_of_stock_items']:,}"
}

# Display KPIs
for kpi, value in kpis.items():
    print(f"{kpi}: {value}")

# Create KPI visualization
fig = make_subplots(
    rows=2, cols=4,
    subplot_titles=list(kpis.keys()),
    specs=[[{"type": "indicator"} for _ in range(4)] for _ in range(2)]
)

# Add KPI indicators
kpi_values = [
    inventory_summary['total_inventory_value'],
    inventory_summary['total_products'],
    len(warehouse_stats),
    inventory_summary['categories_count'],
    inventory_summary['total_available_units'],
    inventory_summary['avg_product_price'],
    optimization_summary['optimization_potential_value'],
    optimization_summary['out_of_stock_items']
]

positions = [(1,1), (1,2), (1,3), (1,4), (2,1), (2,2), (2,3), (2,4)]

for i, (value, pos) in enumerate(zip(kpi_values, positions)):
    fig.add_trace(
        go.Indicator(
            mode="number",
            value=value,
            number={'font': {'size': 20}}
        ),
        row=pos[0], col=pos[1]
    )

fig.update_layout(height=400, title_text="Executive KPI Dashboard")
fig.show()

# Warehouse Performance Dashboard
fig = make_subplots(
    rows=2, cols=2,
    subplot_titles=(
        'Inventory Value by Warehouse',
        'Product Count by Warehouse',
        'Average Product Price by Warehouse',
        'Availability Efficiency by Warehouse'
    )
)

# Inventory value
fig.add_trace(
    go.Bar(
        x=warehouse_stats['warehouse_name'],
        y=warehouse_stats['Total_Value'],
        name='Total Value',
        marker_color='lightblue'
    ),
    row=1, col=1
)

# Product count
fig.add_trace(
    go.Bar(
        x=warehouse_stats['warehouse_name'],
        y=warehouse_stats['Total_Products'],
        name='Product Count',
        marker_color='lightgreen'
    ),
    row=1, col=2
)

# Average price
fig.add_trace(
    go.Bar(
        x=warehouse_stats['warehouse_name'],
        y=warehouse_stats['Avg_Price'],
        name='Avg Price',
        marker_color='orange'
    ),
    row=2, col=1
)

# Availability efficiency
fig.add_trace(
    go.Bar(
        x=warehouse_stats['warehouse_name'],
        y=warehouse_stats['Availability_Efficiency'],
        name='Availability Efficiency',
        marker_color='purple'
    ),
    row=2, col=2
)

fig.update_layout(height=600, showlegend=False, title_text="Warehouse Performance Dashboard")
fig.update_xaxes(tickangle=45)
fig.show()

# Warehouse ranking
print("\n=== WAREHOUSE PERFORMANCE RANKING ===")
warehouse_ranking = warehouse_stats.sort_values('Total_Value', ascending=False)
for i, (_, warehouse) in enumerate(warehouse_ranking.iterrows(), 1):
    print(f"{i}. {warehouse['warehouse_name']}: ${warehouse['Total_Value']:,.2f} ({warehouse['Total_Products']} products)")

# Category Analysis Dashboard
top_categories = category_stats.head(10)

fig = make_subplots(
    rows=2, cols=2,
    subplot_titles=(
        'Top 10 Categories by Value',
        'Top 10 Categories by Product Count',
        'Category Price Distribution',
        'Category Value vs Volume'
    )
)

# Top categories by value
fig.add_trace(
    go.Bar(
        x=top_categories['category'],
        y=top_categories['Total_Value'],
        name='Total Value',
        marker_color='lightcoral'
    ),
    row=1, col=1
)

# Top categories by count
top_by_count = category_stats.nlargest(10, 'Product_Count')
fig.add_trace(
    go.Bar(
        x=top_by_count['category'],
        y=top_by_count['Product_Count'],
        name='Product Count',
        marker_color='lightseagreen'
    ),
    row=1, col=2
)

# Price distribution
fig.add_trace(
    go.Box(
        y=top_categories['Avg_Price'],
        name='Price Distribution',
        marker_color='gold'
    ),
    row=2, col=1
)

# Value vs Volume scatter
fig.add_trace(
    go.Scatter(
        x=top_categories['Total_Available'],
        y=top_categories['Total_Value'],
        mode='markers+text',
        text=top_categories['category'],
        textposition='top center',
        marker=dict(size=10, color='mediumpurple'),
        name='Value vs Volume'
    ),
    row=2, col=2
)

fig.update_layout(height=700, showlegend=False, title_text="Category Analysis Dashboard")
fig.update_xaxes(tickangle=45, row=1)
fig.show()

print("\n=== TOP PERFORMING CATEGORIES ===")
for i, (_, category) in enumerate(top_categories.iterrows(), 1):
    print(f"{i}. {category['category']}: ${category['Total_Value']:,.2f} ({category['Product_Count']} products)")

# Optimization Opportunities Dashboard
stock_distribution = optimization_results['stock_classification'].value_counts()
abc_distribution = optimization_results['abc_category'].value_counts()

fig = make_subplots(
    rows=2, cols=2,
    subplot_titles=(
        'Stock Level Classification',
        'ABC Category Distribution',
        'Stock Variance Distribution',
        'Optimization Potential by Warehouse'
    ),
    specs=[[{"type": "pie"}, {"type": "pie"}],
           [{"type": "histogram"}, {"type": "bar"}]]
)

# Stock classification pie
fig.add_trace(
    go.Pie(
        labels=stock_distribution.index,
        values=stock_distribution.values,
        name="Stock Classification"
    ),
    row=1, col=1
)

# ABC category pie
fig.add_trace(
    go.Pie(
        labels=abc_distribution.index,
        values=abc_distribution.values,
        name="ABC Categories"
    ),
    row=1, col=2
)

# Stock variance histogram
fig.add_trace(
    go.Histogram(
        x=optimization_results['stock_variance_pct'],
        nbinsx=30,
        name="Stock Variance %"
    ),
    row=2, col=1
)

# Optimization potential by warehouse
warehouse_opt = optimization_results.groupby('warehouse_name')['inventory_investment'].sum().sort_values(ascending=False)
fig.add_trace(
    go.Bar(
        x=warehouse_opt.index,
        y=warehouse_opt.values,
        name="Investment by Warehouse"
    ),
    row=2, col=2
)

fig.update_layout(height=700, title_text="Optimization Opportunities Dashboard")
fig.show()

print("\n=== OPTIMIZATION SUMMARY ===")
print(f"Total optimization potential: ${optimization_summary['optimization_potential_value']:,.2f}")
print(f"Percentage of total inventory: {optimization_summary['optimization_potential_pct']:.1f}%")
print(f"High-priority items for rebalancing: {optimization_summary['high_priority_rebalancing']}")

# Strategic Recommendations and Action Plan
print("=== STRATEGIC RECOMMENDATIONS ===")
print("""
IMMEDIATE ACTIONS (0-30 days):
1. Address out-of-stock items ({} products)
2. Redistribute overstock from high-inventory warehouses
3. Implement ABC analysis for procurement prioritization
4. Review pricing strategy for low-turnover premium products

SHORT-TERM INITIATIVES (1-3 months):
1. Implement GraphDB for product relationship mapping
2. Develop cross-selling strategies based on therapeutic areas
3. Optimize warehouse-specific product mix
4. Establish automated reorder points for A-category items

LONG-TERM STRATEGY (3-12 months):
1. Implement predictive analytics for demand forecasting
2. Develop regional specialization strategies
3. Create product bundling programs
4. Establish supplier relationship optimization
""".format(optimization_summary['out_of_stock_items']))

# Generate specific action items
action_items = [
    {
        'Priority': 'HIGH',
        'Action': 'Restock out-of-stock items',
        'Impact': f"{optimization_summary['out_of_stock_items']} products",
        'Timeline': '1-2 weeks'
    },
    {
        'Priority': 'HIGH',
        'Action': 'Redistribute overstock inventory',
        'Impact': f"${optimization_summary['overstock_value']:,.0f} value",
        'Timeline': '2-4 weeks'
    },
    {
        'Priority': 'MEDIUM',
        'Action': 'Implement GraphDB system',
        'Impact': 'Enhanced product relationships',
        'Timeline': '1-2 months'
    },
    {
        'Priority': 'MEDIUM',
        'Action': 'Optimize warehouse specialization',
        'Impact': 'Improved efficiency',
        'Timeline': '2-3 months'
    },
    {
        'Priority': 'LOW',
        'Action': 'Develop predictive analytics',
        'Impact': 'Future optimization',
        'Timeline': '6-12 months'
    }
]

action_df = pd.DataFrame(action_items)
print("\n=== ACTION PLAN ===")
print(action_df.to_string(index=False))

# Final Summary Report
print("=== EXECUTIVE SUMMARY REPORT ===")
print(f"Analysis Date: {datetime.now().strftime('%Y-%m-%d')}")
print(f"\nINVENTORY OVERVIEW:")
print(f"• Total Inventory Value: ${inventory_summary['total_inventory_value']:,.2f}")
print(f"• Total Products: {inventory_summary['total_products']:,}")
print(f"• Active Warehouses: {len(warehouse_stats)}")
print(f"• Product Categories: {inventory_summary['categories_count']}")

print(f"\nPERFORMANCE METRICS:")
best_warehouse = warehouse_stats.loc[warehouse_stats['Total_Value'].idxmax()]
print(f"• Best Performing Warehouse: {best_warehouse['warehouse_name']}")
print(f"• Top Category: {category_stats.iloc[0]['category']}")
print(f"• Average Product Price: ${inventory_summary['avg_product_price']:.2f}")

print(f"\nOPTIMIZATION OPPORTUNITIES:")
print(f"• Total Optimization Potential: ${optimization_summary['optimization_potential_value']:,.2f}")
print(f"• Percentage of Total Inventory: {optimization_summary['optimization_potential_pct']:.1f}%")
print(f"• Out of Stock Items: {optimization_summary['out_of_stock_items']:,}")
print(f"• High-Priority Rebalancing Items: {optimization_summary['high_priority_rebalancing']:,}")

print(f"\nKEY RECOMMENDATIONS:")
print(f"1. Immediate restocking of {optimization_summary['out_of_stock_items']} out-of-stock items")
print(f"2. Redistribute ${optimization_summary['overstock_value']:,.0f} worth of overstock")
print(f"3. Implement GraphDB for enhanced product relationship mapping")
print(f"4. Focus on A-category items representing {optimization_summary['a_category_value_pct']:.1f}% of inventory value")

# Save final report
final_report = {
    'analysis_date': datetime.now().isoformat(),
    'inventory_overview': inventory_summary,
    'optimization_summary': optimization_summary,
    'top_warehouse': best_warehouse.to_dict(),
    'top_category': category_stats.iloc[0].to_dict(),
    'action_items': action_items,
    'files_generated': [
        '01_data_exploration.ipynb',
        '02_warehouse_analysis.ipynb',
        '03_product_category_analysis.ipynb',
        '04_inventory_optimization.ipynb',
        '05_graphdb_product_relationships.ipynb',
        '06_comprehensive_dashboard.ipynb'
    ]
}

with open('final_inventory_analysis_report.json', 'w') as f:
    json.dump(final_report, f, indent=2, default=str)

print(f"\n=== ANALYSIS COMPLETE ===")
print(f"Final report saved to: final_inventory_analysis_report.json")
print(f"Total files generated: {len(final_report['files_generated'])}")
print(f"\nNext steps: Review action items and begin implementation of high-priority recommendations.")