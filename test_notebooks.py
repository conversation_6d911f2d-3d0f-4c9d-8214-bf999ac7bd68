#!/usr/bin/env python3
"""
Notebook Testing Script
Tests all 6 Jupyter notebooks for functionality and data integrity
"""

import pandas as pd
import numpy as np
import ast
import json
from collections import Counter
import sys
import os

def test_data_loading():
    """Test basic data loading functionality"""
    print("🧪 Testing data loading...")
    
    try:
        df = pd.read_csv('Riyadh-Jeddah-Inventory.csv')
        print(f"✅ Raw data loaded: {len(df):,} rows")
        return True
    except FileNotFoundError:
        print("❌ Raw data file not found: Riyadh-Jeddah-Inventory.csv")
        return False
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        return False

def test_data_processing():
    """Test data processing functionality from notebook 01"""
    print("\n🧪 Testing data processing (Notebook 01)...")
    
    try:
        # Load and process data
        df = pd.read_csv('Riyadh-Jeddah-Inventory.csv')
        
        # Test warehouse mapping
        warehouse_mapping = {
            3: 'FDC - Riyadh',
            7: 'WASFATY - Riyadh',
            28: 'WASFATY WL - Riyadh',
            11: 'WASFATY - Jeddah',
            30: 'WASFATY WL - Jeddah'
        }
        
        df['warehouse_name'] = df['warehouse_id'].map(warehouse_mapping)
        df['city'] = df['warehouse_name'].str.extract(r'- (\w+)$')
        df['inventory_value'] = df['price'] * df['available']
        
        # Test category parsing
        def parse_categories(cat_str):
            try:
                if pd.isna(cat_str):
                    return []
                return ast.literal_eval(cat_str)
            except:
                return []
        
        df['categories_list'] = df['categories'].apply(parse_categories)
        
        # Verify results
        assert df['warehouse_name'].isnull().sum() == 0, "Warehouse mapping failed"
        assert df['city'].isnull().sum() == 0, "City extraction failed"
        assert len(df['categories_list'].iloc[0]) >= 0, "Category parsing failed"
        
        print("✅ Data processing successful")
        print(f"   - Warehouses mapped: {df['warehouse_name'].nunique()}")
        print(f"   - Cities identified: {df['city'].nunique()}")
        print(f"   - Total inventory value: ${df['inventory_value'].sum():,.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Data processing failed: {e}")
        return False

def test_warehouse_analysis():
    """Test warehouse analysis functionality from notebook 02"""
    print("\n🧪 Testing warehouse analysis (Notebook 02)...")
    
    try:
        # Check if processed data exists
        try:
            df = pd.read_csv('processed_inventory_data.csv')
            print("✅ Using processed data")
        except FileNotFoundError:
            print("⚠️  Processing raw data...")
            df = pd.read_csv('Riyadh-Jeddah-Inventory.csv')
            
            warehouse_mapping = {
                3: 'FDC - Riyadh',
                7: 'WASFATY - Riyadh',
                28: 'WASFATY WL - Riyadh',
                11: 'WASFATY - Jeddah',
                30: 'WASFATY WL - Jeddah'
            }
            
            df['warehouse_name'] = df['warehouse_id'].map(warehouse_mapping)
            df['city'] = df['warehouse_name'].str.extract(r'- (\w+)$')
            df['inventory_value'] = df['price'] * df['available']
        
        # Test warehouse analysis
        warehouse_stats = df.groupby('warehouse_name').agg({
            'available': ['sum', 'mean'],
            'inventory_value': 'sum',
            'id': 'count'
        }).round(2)
        
        warehouse_stats.columns = ['Total_Available', 'Avg_Available', 'Total_Value', 'Product_Count']
        
        # Verify results
        assert len(warehouse_stats) == 5, f"Expected 5 warehouses, got {len(warehouse_stats)}"
        assert warehouse_stats['Total_Value'].sum() > 0, "No inventory value calculated"
        
        print("✅ Warehouse analysis successful")
        print(f"   - Warehouses analyzed: {len(warehouse_stats)}")
        print(f"   - Total inventory value: ${warehouse_stats['Total_Value'].sum():,.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Warehouse analysis failed: {e}")
        return False

def test_file_generation():
    """Test if expected output files can be generated"""
    print("\n🧪 Testing file generation capabilities...")
    
    try:
        # Test basic data processing and file saving
        df = pd.read_csv('Riyadh-Jeddah-Inventory.csv')
        
        # Create a simple test output
        summary = {
            'total_products': len(df),
            'unique_products': df['id'].nunique(),
            'total_inventory_value': float(df['price'].sum()),
            'warehouses': df['warehouse_id'].unique().tolist()
        }
        
        # Test JSON writing
        with open('test_summary.json', 'w') as f:
            json.dump(summary, f, indent=2)
        
        # Test CSV writing
        test_df = df.head(10)
        test_df.to_csv('test_output.csv', index=False)
        
        # Cleanup test files
        os.remove('test_summary.json')
        os.remove('test_output.csv')
        
        print("✅ File generation successful")
        return True
        
    except Exception as e:
        print(f"❌ File generation failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 NOTEBOOK FUNCTIONALITY TEST SUITE")
    print("=" * 50)
    
    tests = [
        test_data_loading,
        test_data_processing,
        test_warehouse_analysis,
        test_file_generation
    ]
    
    results = []
    for test in tests:
        results.append(test())
    
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"✅ ALL TESTS PASSED ({passed}/{total})")
        print("\n🎉 Notebooks are ready for use!")
        print("\n📋 Next steps:")
        print("1. Run notebooks in sequence (01 → 06)")
        print("2. Review generated analysis files")
        print("3. Implement recommendations")
    else:
        print(f"❌ SOME TESTS FAILED ({passed}/{total})")
        print("\n🔧 Please fix the issues before running notebooks")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
