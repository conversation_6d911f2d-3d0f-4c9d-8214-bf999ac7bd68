# Import libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import warnings
import ast
from collections import Counter
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans

warnings.filterwarnings('ignore')
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

# Load processed data
df = pd.read_csv('processed_inventory_data.csv')
print(f"Loaded {len(df)} records")

# Parse categories and create expanded dataset
def parse_categories(cat_str):
    try:
        if pd.isna(cat_str):
            return []
        return ast.literal_eval(cat_str)
    except:
        return []

df['categories_list'] = df['categories'].apply(parse_categories)

# Create expanded dataset with one row per category
expanded_data = []
for idx, row in df.iterrows():
    categories = row['categories_list']
    if categories:
        for category in categories:
            new_row = row.copy()
            new_row['category'] = category
            expanded_data.append(new_row)
    else:
        new_row = row.copy()
        new_row['category'] = 'Uncategorized'
        expanded_data.append(new_row)

df_expanded = pd.DataFrame(expanded_data)
print(f"Expanded dataset: {len(df_expanded)} records")

# Category performance analysis
category_stats = df_expanded.groupby('category').agg({
    'id': 'count',
    'price': ['mean', 'median', 'std'],
    'available': ['sum', 'mean'],
    'inventory_value': ['sum', 'mean'],
    'warehouse_id': 'nunique'
}).round(2)

category_stats.columns = [
    'Product_Count', 'Avg_Price', 'Median_Price', 'Price_Std',
    'Total_Available', 'Avg_Available', 'Total_Value', 'Avg_Value', 'Warehouses'
]

category_stats = category_stats.reset_index()
category_stats['Price_CV'] = category_stats['Price_Std'] / category_stats['Avg_Price']
category_stats = category_stats.sort_values('Total_Value', ascending=False)

print("=== TOP 15 CATEGORIES BY TOTAL VALUE ===")
print(category_stats.head(15)[['category', 'Product_Count', 'Avg_Price', 'Total_Value', 'Total_Available']])

# Therapeutic area classification
therapeutic_mapping = {
    'Diabetes': 'Endocrine',
    'Hypertension & Heart Disease': 'Cardiovascular',
    'Asthma & Respiratory Disease': 'Respiratory',
    'Prescription Medicine': 'General Prescription',
    'Self Medicine': 'OTC/Self Care',
    'Vitamins & Supplements': 'Nutrition',
    'Cough Relief': 'Respiratory',
    'Flu Cough & Cold': 'Respiratory',
    'Digestive Care': 'Gastrointestinal',
    'Gastro Disease': 'Gastrointestinal',
    'Multivitamines': 'Nutrition',
    'Combined Vitamins & Minerals': 'Nutrition',
    'Diabetic Care': 'Endocrine',
    'Home Care': 'Medical Devices',
    'Syringes & Needles': 'Medical Devices'
}

df_expanded['therapeutic_area'] = df_expanded['category'].map(therapeutic_mapping)
df_expanded['therapeutic_area'] = df_expanded['therapeutic_area'].fillna('Other')

# Therapeutic area analysis
therapeutic_stats = df_expanded.groupby('therapeutic_area').agg({
    'id': 'count',
    'price': 'mean',
    'available': 'sum',
    'inventory_value': 'sum'
}).round(2)

therapeutic_stats.columns = ['Product_Count', 'Avg_Price', 'Total_Available', 'Total_Value']
therapeutic_stats = therapeutic_stats.sort_values('Total_Value', ascending=False)

print("\n=== THERAPEUTIC AREA ANALYSIS ===")
print(therapeutic_stats)

# Create comprehensive category visualization
fig = make_subplots(
    rows=2, cols=2,
    subplot_titles=(
        'Top 10 Categories by Total Value',
        'Top 10 Categories by Product Count',
        'Average Price by Therapeutic Area',
        'Total Available Units by Therapeutic Area'
    )
)

# Top categories by value
top_value_cats = category_stats.head(10)
fig.add_trace(
    go.Bar(x=top_value_cats['category'], y=top_value_cats['Total_Value'],
           name='Total Value', marker_color='lightblue'),
    row=1, col=1
)

# Top categories by count
top_count_cats = category_stats.nlargest(10, 'Product_Count')
fig.add_trace(
    go.Bar(x=top_count_cats['category'], y=top_count_cats['Product_Count'],
           name='Product Count', marker_color='lightgreen'),
    row=1, col=2
)

# Therapeutic area price
fig.add_trace(
    go.Bar(x=therapeutic_stats.index, y=therapeutic_stats['Avg_Price'],
           name='Avg Price', marker_color='orange'),
    row=2, col=1
)

# Therapeutic area availability
fig.add_trace(
    go.Bar(x=therapeutic_stats.index, y=therapeutic_stats['Total_Available'],
           name='Total Available', marker_color='purple'),
    row=2, col=2
)

fig.update_layout(height=800, showlegend=False, title_text="Product Category Analysis Dashboard")
fig.update_xaxes(tickangle=45)
fig.show()

# High-value vs High-volume analysis
# Create product segments based on price and volume
df_unique = df.drop_duplicates(subset=['id'])  # Remove duplicates for this analysis

# Define quartiles for price and volume
price_q75 = df_unique['price'].quantile(0.75)
volume_q75 = df_unique['available'].quantile(0.75)

def classify_product(row):
    if row['price'] >= price_q75 and row['available'] >= volume_q75:
        return 'High-Value High-Volume'
    elif row['price'] >= price_q75 and row['available'] < volume_q75:
        return 'High-Value Low-Volume'
    elif row['price'] < price_q75 and row['available'] >= volume_q75:
        return 'Low-Value High-Volume'
    else:
        return 'Low-Value Low-Volume'

df_unique['product_segment'] = df_unique.apply(classify_product, axis=1)

# Segment analysis
segment_stats = df_unique.groupby('product_segment').agg({
    'id': 'count',
    'price': 'mean',
    'available': 'mean',
    'inventory_value': ['sum', 'mean']
}).round(2)

segment_stats.columns = ['Product_Count', 'Avg_Price', 'Avg_Available', 'Total_Value', 'Avg_Value']

print("=== PRODUCT SEGMENT ANALYSIS ===")
print(segment_stats)

# Visualize segments
plt.figure(figsize=(12, 8))
scatter = plt.scatter(df_unique['price'], df_unique['available'], 
                     c=df_unique['product_segment'].astype('category').cat.codes, 
                     alpha=0.6, s=50)
plt.xlabel('Price (SAR)')
plt.ylabel('Available Units')
plt.title('Product Segmentation: Price vs Volume')
plt.axvline(x=price_q75, color='red', linestyle='--', alpha=0.7, label=f'Price Q75: {price_q75:.2f}')
plt.axhline(y=volume_q75, color='red', linestyle='--', alpha=0.7, label=f'Volume Q75: {volume_q75:.0f}')
plt.legend()
plt.grid(True, alpha=0.3)
plt.show()

# Price analysis by category
print("=== PRICE ANALYSIS BY CATEGORY ===")

# Categories with highest price variability
high_variability = category_stats.nlargest(10, 'Price_CV')[['category', 'Avg_Price', 'Price_Std', 'Price_CV']]
print("\nCategories with highest price variability:")
print(high_variability)

# Premium vs Budget categories
premium_threshold = df_unique['price'].quantile(0.8)
budget_threshold = df_unique['price'].quantile(0.2)

premium_categories = category_stats[category_stats['Avg_Price'] >= premium_threshold]['category'].tolist()
budget_categories = category_stats[category_stats['Avg_Price'] <= budget_threshold]['category'].tolist()

print(f"\nPremium categories (avg price >= {premium_threshold:.2f}):")
print(premium_categories)

print(f"\nBudget categories (avg price <= {budget_threshold:.2f}):")
print(budget_categories)

# Category distribution across warehouses
category_warehouse = pd.crosstab(df_expanded['category'], df_expanded['warehouse_name'], normalize='columns') * 100

# Get top 10 categories for visualization
top_categories = category_stats.head(10)['category'].tolist()
category_warehouse_top = category_warehouse.loc[top_categories]

plt.figure(figsize=(15, 8))
sns.heatmap(category_warehouse_top, annot=True, fmt='.1f', cmap='YlOrRd')
plt.title('Top 10 Categories Distribution Across Warehouses (%)')
plt.xlabel('Warehouse')
plt.ylabel('Category')
plt.xticks(rotation=45)
plt.yticks(rotation=0)
plt.tight_layout()
plt.show()

print("\n=== CATEGORY SPECIALIZATION BY WAREHOUSE ===")
for warehouse in category_warehouse.columns:
    top_cats = category_warehouse[warehouse].nlargest(3)
    print(f"\n{warehouse}:")
    for cat, pct in top_cats.items():
        print(f"  {cat}: {pct:.1f}%")

# Save analysis results
category_stats.to_csv('category_performance_analysis.csv', index=False)
therapeutic_stats.to_csv('therapeutic_area_analysis.csv')
segment_stats.to_csv('product_segment_analysis.csv')
df_unique[['id', 'name', 'price', 'available', 'product_segment']].to_csv('product_segments.csv', index=False)

# Create summary insights
insights = {
    'total_categories': len(category_stats),
    'top_category_by_value': category_stats.iloc[0]['category'],
    'top_category_value': category_stats.iloc[0]['Total_Value'],
    'most_expensive_category': category_stats.loc[category_stats['Avg_Price'].idxmax(), 'category'],
    'highest_volume_category': category_stats.loc[category_stats['Total_Available'].idxmax(), 'category'],
    'therapeutic_areas': len(therapeutic_stats),
    'top_therapeutic_area': therapeutic_stats.index[0],
    'premium_categories_count': len(premium_categories),
    'budget_categories_count': len(budget_categories)
}

import json
with open('category_analysis_insights.json', 'w') as f:
    json.dump(insights, f, indent=2)

print("\n=== CATEGORY ANALYSIS SUMMARY ===")
for key, value in insights.items():
    print(f"{key}: {value}")

print("\nFiles saved:")
print("- category_performance_analysis.csv")
print("- therapeutic_area_analysis.csv")
print("- product_segment_analysis.csv")
print("- product_segments.csv")
print("- category_analysis_insights.json")