
# !pip install pandas numpy matplotlib seaborn plotly networkx scikit-learn

# Import required libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import warnings
import ast
from collections import Counter

warnings.filterwarnings('ignore')
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

# Configure display options
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', 100)

# Load the inventory data
df = pd.read_csv('Riyadh-Jeddah-Inventory.csv')

print(f"Dataset shape: {df.shape}")
print(f"\nColumns: {list(df.columns)}")
print(f"\nFirst few rows:")
df.head()

# Basic data information
print("=== DATA TYPES AND NULL VALUES ===")
print(df.info())
print("\n=== MISSING VALUES ===")
missing_data = df.isnull().sum()
missing_percent = (missing_data / len(df)) * 100
missing_df = pd.DataFrame({
    'Missing Count': missing_data,
    'Missing Percentage': missing_percent
})
print(missing_df[missing_df['Missing Count'] > 0])

# Create warehouse mapping
warehouse_mapping = {
    3: 'FDC - Riyadh',
    7: 'WASFATY - Riyadh',
    28: 'WASFATY WL - Riyadh',
    11: 'WASFATY - Jeddah',
    30: 'WASFATY WL - Jeddah'
}

df['warehouse_name'] = df['warehouse_id'].map(warehouse_mapping)
df['city'] = df['warehouse_name'].str.extract(r'- (\w+)$')

print("=== WAREHOUSE DISTRIBUTION ===")
warehouse_dist = df['warehouse_name'].value_counts()
print(warehouse_dist)

print("\n=== CITY DISTRIBUTION ===")
city_dist = df['city'].value_counts()
print(city_dist)

# Basic statistics for numerical columns
print("=== NUMERICAL STATISTICS ===")
numerical_cols = ['price', 'available', 'on_hand']
print(df[numerical_cols].describe())

print("\n=== UNIQUE VALUES ===")
for col in df.columns:
    unique_count = df[col].nunique()
    print(f"{col}: {unique_count} unique values")

# Parse categories column (it appears to be in string format of list)
def parse_categories(cat_str):
    try:
        if pd.isna(cat_str):
            return []
        # Remove extra quotes and parse as list
        return ast.literal_eval(cat_str)
    except:
        return []

df['categories_list'] = df['categories'].apply(parse_categories)

# Get all unique categories
all_categories = []
for cat_list in df['categories_list']:
    all_categories.extend(cat_list)

category_counts = Counter(all_categories)
print("=== TOP 15 PRODUCT CATEGORIES ===")
for cat, count in category_counts.most_common(15):
    print(f"{cat}: {count} products")

# Inventory value analysis
df['inventory_value'] = df['price'] * df['available']
df['total_value'] = df['price'] * df['on_hand']

print("=== INVENTORY VALUE ANALYSIS ===")
print(f"Total Available Inventory Value: ${df['inventory_value'].sum():,.2f}")
print(f"Total On-Hand Inventory Value: ${df['total_value'].sum():,.2f}")
print(f"Average Product Price: ${df['price'].mean():.2f}")
print(f"Median Product Price: ${df['price'].median():.2f}")

print("\n=== INVENTORY BY WAREHOUSE ===")
warehouse_summary = df.groupby('warehouse_name').agg({
    'available': ['sum', 'mean'],
    'inventory_value': 'sum',
    'id': 'count'
}).round(2)
warehouse_summary.columns = ['Total_Available', 'Avg_Available', 'Total_Value', 'Product_Count']
print(warehouse_summary)

# Data quality checks
print("=== DATA QUALITY CHECKS ===")

# Check for negative values
negative_price = (df['price'] < 0).sum()
negative_available = (df['available'] < 0).sum()
negative_on_hand = (df['on_hand'] < 0).sum()

print(f"Negative prices: {negative_price}")
print(f"Negative available: {negative_available}")
print(f"Negative on_hand: {negative_on_hand}")

# Check for zero values
zero_price = (df['price'] == 0).sum()
zero_available = (df['available'] == 0).sum()
zero_on_hand = (df['on_hand'] == 0).sum()

print(f"\nZero prices: {zero_price}")
print(f"Zero available: {zero_available}")
print(f"Zero on_hand: {zero_on_hand}")

# Check for duplicates
duplicate_ids = df['id'].duplicated().sum()
duplicate_skus = df['sku'].duplicated().sum()

print(f"\nDuplicate IDs: {duplicate_ids}")
print(f"Duplicate SKUs: {duplicate_skus}")

# Check available vs on_hand consistency
inconsistent_stock = (df['available'] != df['on_hand']).sum()
print(f"\nInconsistent available vs on_hand: {inconsistent_stock}")

# Save processed data for next notebooks
df.to_csv('processed_inventory_data.csv', index=False)
print("Processed data saved to 'processed_inventory_data.csv'")

# Create summary statistics file
summary_stats = {
    'total_products': len(df),
    'unique_products': df['id'].nunique(),
    'total_warehouses': df['warehouse_id'].nunique(),
    'total_inventory_value': df['inventory_value'].sum(),
    'avg_product_price': df['price'].mean(),
    'total_available_units': df['available'].sum(),
    'categories_count': len(category_counts)
}

import json
with open('inventory_summary.json', 'w') as f:
    json.dump(summary_stats, f, indent=2)

print("\n=== SUMMARY STATISTICS ===")
for key, value in summary_stats.items():
    if isinstance(value, float):
        print(f"{key}: {value:,.2f}")
    else:
        print(f"{key}: {value:,}")

