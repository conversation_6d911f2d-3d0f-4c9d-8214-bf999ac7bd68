# Import libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import warnings
from scipy import stats
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans

warnings.filterwarnings('ignore')
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

# Load processed data
df = pd.read_csv('processed_inventory_data.csv')
print(f"Loaded {len(df)} records")

# Remove duplicates for optimization analysis
df_unique = df.drop_duplicates(subset=['id', 'warehouse_id'])
print(f"Unique product-warehouse combinations: {len(df_unique)}")

# Calculate inventory metrics
# Since we don't have sales data, we'll use statistical methods to estimate optimal levels

# Calculate basic inventory metrics
df_unique['days_of_supply'] = df_unique['available'] / (df_unique['available'].mean() / 30)  # Estimated daily usage
df_unique['inventory_investment'] = df_unique['price'] * df_unique['available']
df_unique['stock_density'] = df_unique['available'] / df_unique['price']  # Units per SAR

# Calculate percentiles for stock level classification
stock_percentiles = df_unique['available'].quantile([0.1, 0.25, 0.75, 0.9])
print("=== STOCK LEVEL PERCENTILES ===")
print(f"10th percentile: {stock_percentiles[0.1]:.0f}")
print(f"25th percentile: {stock_percentiles[0.25]:.0f}")
print(f"75th percentile: {stock_percentiles[0.75]:.0f}")
print(f"90th percentile: {stock_percentiles[0.9]:.0f}")

# Classify stock levels
def classify_stock_level(available):
    if available == 0:
        return 'Out of Stock'
    elif available <= stock_percentiles[0.1]:
        return 'Critically Low'
    elif available <= stock_percentiles[0.25]:
        return 'Low Stock'
    elif available <= stock_percentiles[0.75]:
        return 'Normal Stock'
    elif available <= stock_percentiles[0.9]:
        return 'High Stock'
    else:
        return 'Overstocked'

df_unique['stock_classification'] = df_unique['available'].apply(classify_stock_level)

# Stock classification summary
stock_summary = df_unique['stock_classification'].value_counts()
print("\n=== STOCK CLASSIFICATION SUMMARY ===")
print(stock_summary)
print(f"\nPercentage distribution:")
print((stock_summary / len(df_unique) * 100).round(2))

# Identify optimization opportunities
print("=== OPTIMIZATION OPPORTUNITIES ===")

# Overstocked items (potential for redistribution or reduction)
overstocked = df_unique[df_unique['stock_classification'] == 'Overstocked'].copy()
overstocked = overstocked.sort_values('inventory_investment', ascending=False)

print(f"\nOverstocked items: {len(overstocked)}")
print(f"Total value tied up in overstock: ${overstocked['inventory_investment'].sum():,.2f}")
print("\nTop 10 overstocked items by value:")
print(overstocked[['name', 'warehouse_name', 'available', 'price', 'inventory_investment']].head(10))

# Understocked items (potential stockouts)
understocked = df_unique[df_unique['stock_classification'].isin(['Critically Low', 'Low Stock'])].copy()
understocked = understocked.sort_values('inventory_investment', ascending=False)

print(f"\n\nUnderstocked items: {len(understocked)}")
print(f"Potential lost sales value: ${understocked['inventory_investment'].sum():,.2f}")
print("\nTop 10 understocked high-value items:")
print(understocked[['name', 'warehouse_name', 'available', 'price', 'inventory_investment']].head(10))

# ABC Analysis (Pareto Analysis)
# Classify products based on their inventory value contribution

# Calculate cumulative value percentage
df_abc = df_unique.sort_values('inventory_investment', ascending=False).copy()
df_abc['cumulative_value'] = df_abc['inventory_investment'].cumsum()
df_abc['cumulative_value_pct'] = df_abc['cumulative_value'] / df_abc['inventory_investment'].sum() * 100
df_abc['cumulative_count_pct'] = (df_abc.index + 1) / len(df_abc) * 100

# Classify into ABC categories
def abc_classification(cum_value_pct):
    if cum_value_pct <= 80:
        return 'A'
    elif cum_value_pct <= 95:
        return 'B'
    else:
        return 'C'

df_abc['abc_category'] = df_abc['cumulative_value_pct'].apply(abc_classification)

# ABC summary
abc_summary = df_abc.groupby('abc_category').agg({
    'id': 'count',
    'inventory_investment': 'sum',
    'available': 'sum'
}).round(2)

abc_summary['value_percentage'] = abc_summary['inventory_investment'] / abc_summary['inventory_investment'].sum() * 100
abc_summary['count_percentage'] = abc_summary['id'] / abc_summary['id'].sum() * 100

print("=== ABC ANALYSIS ===")
print(abc_summary)

# Visualize ABC analysis
plt.figure(figsize=(12, 6))
plt.plot(df_abc['cumulative_count_pct'], df_abc['cumulative_value_pct'], 'b-', linewidth=2)
plt.axhline(y=80, color='r', linestyle='--', alpha=0.7, label='80% Value (A items)')
plt.axhline(y=95, color='orange', linestyle='--', alpha=0.7, label='95% Value (B items)')
plt.xlabel('Cumulative % of Products')
plt.ylabel('Cumulative % of Inventory Value')
plt.title('ABC Analysis - Pareto Chart')
plt.grid(True, alpha=0.3)
plt.legend()
plt.show()

# Warehouse-specific optimization
warehouse_optimization = df_unique.groupby(['warehouse_name', 'stock_classification']).agg({
    'id': 'count',
    'inventory_investment': 'sum'
}).round(2)

print("=== WAREHOUSE-SPECIFIC STOCK ISSUES ===")
print(warehouse_optimization)

# Calculate optimization potential by warehouse
warehouse_issues = df_unique.groupby('warehouse_name').agg({
    'id': 'count',
    'inventory_investment': 'sum'
})

# Add overstock and understock metrics
overstock_by_warehouse = overstocked.groupby('warehouse_name')['inventory_investment'].sum()
understock_by_warehouse = understocked.groupby('warehouse_name')['inventory_investment'].sum()

warehouse_issues['overstock_value'] = overstock_by_warehouse.fillna(0)
warehouse_issues['understock_value'] = understock_by_warehouse.fillna(0)
warehouse_issues['optimization_potential'] = warehouse_issues['overstock_value'] + warehouse_issues['understock_value']
warehouse_issues['optimization_pct'] = warehouse_issues['optimization_potential'] / warehouse_issues['inventory_investment'] * 100

print("\n=== WAREHOUSE OPTIMIZATION POTENTIAL ===")
print(warehouse_issues.round(2))

# Recommended actions and optimal stock levels
print("=== OPTIMIZATION RECOMMENDATIONS ===")

# Calculate recommended stock levels based on statistical analysis
category_stats = df_unique.groupby('categories')['available'].agg(['mean', 'std', 'median']).fillna(0)

def calculate_optimal_stock(row):
    # Use category median as baseline, adjusted for price tier
    try:
        category = row['categories']
        if category in category_stats.index:
            baseline = category_stats.loc[category, 'median']
        else:
            baseline = df_unique['available'].median()
        
        # Adjust based on price (higher price = lower optimal stock)
        price_factor = 1 - (row['price'] - df_unique['price'].median()) / df_unique['price'].std() * 0.1
        price_factor = max(0.5, min(1.5, price_factor))  # Constrain between 0.5 and 1.5
        
        optimal = baseline * price_factor
        return max(10, optimal)  # Minimum stock of 10 units
    except:
        return df_unique['available'].median()

df_unique['optimal_stock'] = df_unique.apply(calculate_optimal_stock, axis=1)
df_unique['stock_variance'] = df_unique['available'] - df_unique['optimal_stock']
df_unique['stock_variance_pct'] = df_unique['stock_variance'] / df_unique['optimal_stock'] * 100

# Generate specific recommendations
recommendations = []

# High-priority rebalancing opportunities
high_overstock = df_unique[(df_unique['stock_variance_pct'] > 100) & (df_unique['inventory_investment'] > 1000)]
high_understock = df_unique[(df_unique['stock_variance_pct'] < -50) & (df_unique['price'] > 50)]

print(f"\nHigh-priority overstock items (>100% above optimal, >1000 SAR value): {len(high_overstock)}")
print(f"High-priority understock items (<50% of optimal, >50 SAR price): {len(high_understock)}")

# Top recommendations
print("\n=== TOP 10 REBALANCING RECOMMENDATIONS ===")
print("\nREDUCE STOCK (Overstocked):")
reduce_stock = high_overstock.nlargest(10, 'inventory_investment')[['name', 'warehouse_name', 'available', 'optimal_stock', 'stock_variance', 'inventory_investment']]
print(reduce_stock)

print("\nINCREASE STOCK (Understocked):")
increase_stock = high_understock.nlargest(10, 'price')[['name', 'warehouse_name', 'available', 'optimal_stock', 'stock_variance', 'price']]
print(increase_stock)

# Create optimization dashboard
fig = make_subplots(
    rows=2, cols=2,
    subplot_titles=(
        'Stock Classification Distribution',
        'ABC Category Value Distribution',
        'Optimization Potential by Warehouse',
        'Stock Variance Distribution'
    ),
    specs=[[{"type": "pie"}, {"type": "bar"}],
           [{"type": "bar"}, {"type": "histogram"}]]
)

# Stock classification pie chart
fig.add_trace(
    go.Pie(labels=stock_summary.index, values=stock_summary.values, name="Stock Classification"),
    row=1, col=1
)

# ABC category bar chart
fig.add_trace(
    go.Bar(x=abc_summary.index, y=abc_summary['value_percentage'], name="ABC Value %"),
    row=1, col=2
)

# Optimization potential by warehouse
fig.add_trace(
    go.Bar(x=warehouse_issues.index, y=warehouse_issues['optimization_pct'], name="Optimization %"),
    row=2, col=1
)

# Stock variance histogram
fig.add_trace(
    go.Histogram(x=df_unique['stock_variance_pct'], nbinsx=50, name="Stock Variance %"),
    row=2, col=2
)

fig.update_layout(height=800, showlegend=False, title_text="Inventory Optimization Dashboard")
fig.show()

# Save optimization results
optimization_results = df_unique[[
    'id', 'name', 'sku', 'warehouse_name', 'available', 'optimal_stock', 
    'stock_variance', 'stock_variance_pct', 'stock_classification', 
    'abc_category', 'inventory_investment', 'price'
]].copy()

optimization_results.to_csv('inventory_optimization_results.csv', index=False)
overstocked[['id', 'name', 'warehouse_name', 'available', 'inventory_investment']].to_csv('overstocked_items.csv', index=False)
understocked[['id', 'name', 'warehouse_name', 'available', 'price']].to_csv('understocked_items.csv', index=False)
warehouse_issues.to_csv('warehouse_optimization_summary.csv')

# Create executive summary
summary = {
    'total_inventory_value': df_unique['inventory_investment'].sum(),
    'overstock_value': overstocked['inventory_investment'].sum(),
    'understock_count': len(understocked),
    'optimization_potential_value': warehouse_issues['optimization_potential'].sum(),
    'optimization_potential_pct': warehouse_issues['optimization_potential'].sum() / df_unique['inventory_investment'].sum() * 100,
    'a_category_items': len(df_abc[df_abc['abc_category'] == 'A']),
    'a_category_value_pct': abc_summary.loc['A', 'value_percentage'],
    'out_of_stock_items': len(df_unique[df_unique['available'] == 0]),
    'high_priority_rebalancing': len(high_overstock) + len(high_understock)
}

import json
with open('optimization_summary.json', 'w') as f:
    json.dump(summary, f, indent=2)

print("\n=== OPTIMIZATION SUMMARY ===")
print(f"Total inventory value: ${summary['total_inventory_value']:,.2f}")
print(f"Overstock value: ${summary['overstock_value']:,.2f}")
print(f"Understocked items: {summary['understock_count']:,}")
print(f"Optimization potential: ${summary['optimization_potential_value']:,.2f} ({summary['optimization_potential_pct']:.1f}%)")
print(f"High-priority rebalancing items: {summary['high_priority_rebalancing']:,}")
print(f"Out of stock items: {summary['out_of_stock_items']:,}")

print("\nFiles saved:")
print("- inventory_optimization_results.csv")
print("- overstocked_items.csv")
print("- understocked_items.csv")
print("- warehouse_optimization_summary.csv")
print("- optimization_summary.json")