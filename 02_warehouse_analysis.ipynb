!pip install nbformat
# Import libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import warnings
import ast
from collections import Counter

warnings.filterwarnings('ignore')
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

# Load processed data
df = pd.read_csv('processed_inventory_data.csv')
print(f"Loaded {len(df)} records from processed data")

# Warehouse performance overview
warehouse_stats = df.groupby(['warehouse_id', 'warehouse_name', 'city']).agg({
    'id': 'count',
    'available': ['sum', 'mean', 'std'],
    'price': ['mean', 'std'],
    'inventory_value': ['sum', 'mean'],
    'sku': 'nunique'
}).round(2)

warehouse_stats.columns = [
    'Total_Products', 'Total_Available', 'Avg_Available', 'Std_Available',
    'Avg_Price', 'Std_Price', 'Total_Value', 'Avg_Value', 'Unique_SKUs'
]

warehouse_stats = warehouse_stats.reset_index()
warehouse_stats['Value_per_Product'] = warehouse_stats['Total_Value'] / warehouse_stats['Total_Products']
warehouse_stats['Availability_Efficiency'] = warehouse_stats['Total_Available'] / warehouse_stats['Total_Products']

print("=== WAREHOUSE PERFORMANCE OVERVIEW ===")
print(warehouse_stats)

# Create comprehensive warehouse comparison visualizations
fig = make_subplots(
    rows=2, cols=2,
    subplot_titles=(
        'Total Inventory Value by Warehouse',
        'Total Available Units by Warehouse', 
        'Average Product Price by Warehouse',
        'Product Count by Warehouse'
    ),
    specs=[[{"secondary_y": False}, {"secondary_y": False}],
           [{"secondary_y": False}, {"secondary_y": False}]]
)

# Total Value
fig.add_trace(
    go.Bar(x=warehouse_stats['warehouse_name'], y=warehouse_stats['Total_Value'],
           name='Total Value', marker_color='lightblue'),
    row=1, col=1
)

# Total Available
fig.add_trace(
    go.Bar(x=warehouse_stats['warehouse_name'], y=warehouse_stats['Total_Available'],
           name='Total Available', marker_color='lightgreen'),
    row=1, col=2
)

# Average Price
fig.add_trace(
    go.Bar(x=warehouse_stats['warehouse_name'], y=warehouse_stats['Avg_Price'],
           name='Avg Price', marker_color='orange'),
    row=2, col=1
)

# Product Count
fig.add_trace(
    go.Bar(x=warehouse_stats['warehouse_name'], y=warehouse_stats['Total_Products'],
           name='Product Count', marker_color='purple'),
    row=2, col=2
)

fig.update_layout(height=800, showlegend=False, title_text="Warehouse Performance Dashboard")
fig.update_xaxes(tickangle=45)
fig.show()

# City-level analysis
city_analysis = df.groupby('city').agg({
    'warehouse_id': 'nunique',
    'id': 'count',
    'available': 'sum',
    'inventory_value': 'sum',
    'price': 'mean'
}).round(2)

city_analysis.columns = ['Warehouses', 'Products', 'Total_Available', 'Total_Value', 'Avg_Price']
city_analysis['Value_per_Product'] = city_analysis['Total_Value'] / city_analysis['Products']

print("=== CITY-LEVEL ANALYSIS ===")
print(city_analysis)

# Visualize city comparison
fig, axes = plt.subplots(2, 2, figsize=(15, 10))
fig.suptitle('City-Level Inventory Comparison', fontsize=16)

# Total Value by City
city_analysis['Total_Value'].plot(kind='bar', ax=axes[0,0], color='skyblue')
axes[0,0].set_title('Total Inventory Value by City')
axes[0,0].set_ylabel('Value (SAR)')
axes[0,0].tick_params(axis='x', rotation=45)

# Total Products by City
city_analysis['Products'].plot(kind='bar', ax=axes[0,1], color='lightgreen')
axes[0,1].set_title('Total Products by City')
axes[0,1].set_ylabel('Number of Products')
axes[0,1].tick_params(axis='x', rotation=45)

# Available Units by City
city_analysis['Total_Available'].plot(kind='bar', ax=axes[1,0], color='orange')
axes[1,0].set_title('Total Available Units by City')
axes[1,0].set_ylabel('Available Units')
axes[1,0].tick_params(axis='x', rotation=45)

# Average Price by City
city_analysis['Avg_Price'].plot(kind='bar', ax=axes[1,1], color='purple')
axes[1,1].set_title('Average Product Price by City')
axes[1,1].set_ylabel('Price (SAR)')
axes[1,1].tick_params(axis='x', rotation=45)

plt.tight_layout()
plt.show()

# Stock level distribution analysis
print("=== STOCK LEVEL DISTRIBUTION BY WAREHOUSE ===")

# Define stock level categories
def categorize_stock(available):
    if available == 0:
        return 'Out of Stock'
    elif available <= 50:
        return 'Low Stock'
    elif available <= 200:
        return 'Medium Stock'
    elif available <= 500:
        return 'High Stock'
    else:
        return 'Very High Stock'

df['stock_category'] = df['available'].apply(categorize_stock)

# Stock distribution by warehouse
stock_dist = pd.crosstab(df['warehouse_name'], df['stock_category'], normalize='index') * 100
print(stock_dist.round(2))

# Visualize stock distribution
stock_dist.plot(kind='bar', stacked=True, figsize=(12, 6), 
                title='Stock Level Distribution by Warehouse (%)')
plt.ylabel('Percentage of Products')
plt.xlabel('Warehouse')
plt.xticks(rotation=45)
plt.legend(title='Stock Category', bbox_to_anchor=(1.05, 1), loc='upper left')
plt.tight_layout()
plt.show()

# Top products by warehouse
print("=== TOP 5 HIGHEST VALUE PRODUCTS BY WAREHOUSE ===")

for warehouse_id in df['warehouse_id'].unique():
    warehouse_data = df[df['warehouse_id'] == warehouse_id]
    warehouse_name = warehouse_data['warehouse_name'].iloc[0]
    
    top_products = warehouse_data.nlargest(5, 'inventory_value')[['name', 'price', 'available', 'inventory_value']]
    
    print(f"\n{warehouse_name}:")
    print(top_products.to_string(index=False))

# Warehouse efficiency metrics
print("=== WAREHOUSE EFFICIENCY METRICS ===")

efficiency_metrics = df.groupby(['warehouse_id', 'warehouse_name']).agg({
    'available': lambda x: (x > 0).sum() / len(x),  # Stock availability rate
    'inventory_value': ['sum', 'std'],
    'price': 'std'
}).round(4)

efficiency_metrics.columns = ['Stock_Availability_Rate', 'Total_Value', 'Value_Std', 'Price_Std']
efficiency_metrics = efficiency_metrics.reset_index()

# Calculate coefficient of variation for inventory value
efficiency_metrics['Value_CV'] = efficiency_metrics['Value_Std'] / (efficiency_metrics['Total_Value'] / len(df))

print(efficiency_metrics)

# Visualize efficiency metrics
fig, axes = plt.subplots(1, 2, figsize=(15, 5))

# Stock availability rate
axes[0].bar(efficiency_metrics['warehouse_name'], efficiency_metrics['Stock_Availability_Rate'])
axes[0].set_title('Stock Availability Rate by Warehouse')
axes[0].set_ylabel('Availability Rate')
axes[0].tick_params(axis='x', rotation=45)

# Value coefficient of variation
axes[1].bar(efficiency_metrics['warehouse_name'], efficiency_metrics['Value_CV'])
axes[1].set_title('Inventory Value Variability by Warehouse')
axes[1].set_ylabel('Coefficient of Variation')
axes[1].tick_params(axis='x', rotation=45)

plt.tight_layout()
plt.show()

# Save warehouse analysis results
warehouse_stats.to_csv('warehouse_performance_stats.csv', index=False)
city_analysis.to_csv('city_analysis.csv')
efficiency_metrics.to_csv('warehouse_efficiency_metrics.csv', index=False)

print("\n=== WAREHOUSE ANALYSIS SUMMARY ===")
print(f"Total warehouses analyzed: {len(warehouse_stats)}")
print(f"Best performing warehouse (by total value): {warehouse_stats.loc[warehouse_stats['Total_Value'].idxmax(), 'warehouse_name']}")
print(f"Most efficient warehouse (by availability rate): {efficiency_metrics.loc[efficiency_metrics['Stock_Availability_Rate'].idxmax(), 'warehouse_name']}")
print(f"Highest average price warehouse: {warehouse_stats.loc[warehouse_stats['Avg_Price'].idxmax(), 'warehouse_name']}")

print("\nFiles saved:")
print("- warehouse_performance_stats.csv")
print("- city_analysis.csv")
print("- warehouse_efficiency_metrics.csv")